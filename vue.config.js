const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  transpileDependencies: true,
  devServer: {
    proxy: {
      // 代理所有以 /api 开头的请求
      '/api-j': {
        target: 'http://*************:10097', // 目标服务器地址
        changeOrigin: true, // 改变请求头中的host
        ws: true, // 支持websocket
      },
      // 可以配置多个代理规则
      '/auth': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        ws: true,
      }
    }
  }
})
